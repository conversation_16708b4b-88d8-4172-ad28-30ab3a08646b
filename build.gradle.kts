plugins {
    id("java-base")
    id("org.sonarqube") version "4.4.1.3373"
    id("io.gitlab.arturbosch.detekt") version "1.23.8"
    id("org.jetbrains.kotlin.jvm") version "1.9.25"
    id("com.google.cloud.tools.jib") version "3.4.2" apply false
}

dependencies {
    detektPlugins("io.gitlab.arturbosch.detekt:detekt-formatting:1.23.8")
}

sonarqube {
    properties {
        property(
            "sonar.exclusions",
            "**/com/hellofresh/scm/*/*Configuration.kt," +
                    "**/com/hellofresh/scm/*/*Application.kt,",
        )
        property(
            "sonar.coverage.exclusions",
            "**/com/hellofresh/scm/model/**/*"
        )
        property("sonar.host.url", "https://sonarqube.tools-k8s.hellofresh.io")
        property("sonar.links.homepage", "https://github.com/hellofresh/scm-procurement-data-integration")
        property("sonar.links.ci", "https://ci.hellofresh.io/teams/scm.procurement/pipelines/scm-procurement-data-integration")
        property("sonar.links.scm", "https://github.com/hellofresh/scm-procurement-data-integration")
        property("sonar.links.scm_dev", "**************:hellofresh/scm-procurement-data-integration.git")
        property("sonar.gradle.skipCompile", "true")
        // property(
        //     "sonar.coverage.jacoco.xmlReportPaths",
        //     "${project.layout.buildDirectory.get().asFile}/reports/jacoco/jacocoAggregatedReport/jacocoAggregatedReport.xml",
        // )
    }
}

tasks {
    withType<org.jetbrains.kotlin.gradle.tasks.KotlinCompile> {
        kotlinOptions {
            jvmTarget = "21"
        }
    }

    withType<io.gitlab.arturbosch.detekt.Detekt>().configureEach {
        val javaVersion = "21"

        config.from("$rootDir/.detekt.yaml")

        ignoreFailures = false
        jvmTarget = javaVersion
        buildUponDefaultConfig = true
        parallel = true

        setSource(files(projectDir))
        include("**/*.kt", "**/*.kts")
        exclude("**/resources/**", "**/build/**")

        val ci = System.getenv().containsKey("CI")
        reports {
            txt.required.set(false)
            html.required.set(!ci)
            xml.required.set(ci)
        }
    }

    register<io.gitlab.arturbosch.detekt.Detekt>("detektFormat") {
        description = "Reformat all Kotlin files."
        autoCorrect = true
        ignoreFailures = true
    }

    check {
        dependsOn("detekt")
    }

    val cleanDetekt by registering(Delete::class) {
        setDelete(layout.buildDirectory.dir("reports/detekt"))
    }

    clean {
        dependsOn(cleanDetekt)
    }
}
