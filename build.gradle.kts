import io.gitlab.arturbosch.detekt.Detekt
import org.jetbrains.kotlin.gradle.dsl.JvmTarget

plugins {
    id("java-base")
    alias(libs.plugins.jacocolog)
    alias(libs.plugins.sonarqube)
    alias(libs.plugins.detekt.gradle)
    alias(libs.plugins.kotlin.jvm)
    id("com.google.cloud.tools.jib") version "3.4.2" apply false
}

dependencies {
    detektPlugins("io.gitlab.arturbosch.detekt:detekt-formatting:1.23.8")
}

sonarqube {
    properties {
        property(
            "sonar.exclusions",
            "**/com/hellofresh/scm/*/*Configuration.kt," +
                    "**/com/hellofresh/scm/*/*Application.kt,",
        )
        property(
            "sonar.coverage.exclusions",
            "**/com/hellofresh/scm/model/**/*"
        )
        property("sonar.host.url", "https://sonarqube.tools-k8s.hellofresh.io")
        property("sonar.links.homepage", "https://github.com/hellofresh/scm-procurement-data-integration")
        property("sonar.links.ci", "https://ci.hellofresh.io/teams/scm.procurement/pipelines/scm-procurement-data-integration")
        property("sonar.links.scm", "https://github.com/hellofresh/scm-procurement-data-integration")
        property("sonar.links.scm_dev", "**************:hellofresh/scm-procurement-data-integration.git")
        property("sonar.gradle.skipCompile", "true")
        property(
            "sonar.coverage.jacoco.xmlReportPaths",
            "${project.layout.buildDirectory.get().asFile}/reports/jacoco/jacocoAggregatedReport/jacocoAggregatedReport.xml",
        )
    }
}

tasks {
    compileKotlin {
        compilerOptions {
            jvmTarget = JvmTarget.JVM_21
        }
    }

    withType<Detekt>().configureEach {
        val javaVersion = "21"

        config.from("$rootDir/.detekt.yaml")

        ignoreFailures = false
        jvmTarget = javaVersion
        buildUponDefaultConfig = true
        parallel = true

        setSource(files(projectDir))
        include("**/*.kt", "**/*.kts")
        exclude("**/resources/**", "**/build/**")

        val ci = System.getenv().containsKey("CI")
        reports {
            txt.required.set(false)
            html.required.set(!ci)
            xml.required.set(ci)
        }
    }

    register<Detekt>("detektFormat") {
        description = "Reformat all Kotlin files."
        autoCorrect = true
        ignoreFailures = true
    }

    check {
        dependsOn("detekt")
    }

    val cleanDetekt by registering(Delete::class) {
        setDelete(detekt.get().reportsDir.get())
    }

    clean {
        dependsOn(cleanDetekt)
    }
}
