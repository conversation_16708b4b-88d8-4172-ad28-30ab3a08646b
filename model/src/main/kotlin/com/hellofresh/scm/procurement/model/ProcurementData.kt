package com.hellofresh.scm.procurement.model

import com.fasterxml.jackson.annotation.JsonProperty
import jakarta.persistence.*
import java.time.LocalDateTime

@Entity
@Table(name = "procurement_data")
data class ProcurementData(
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    val id: Long? = null,
    
    @Column(name = "supplier_name", nullable = false)
    @JsonProperty("supplier_name")
    val supplierName: String,
    
    @Column(name = "product_name", nullable = false)
    @JsonProperty("product_name")
    val productName: String,
    
    @Column(name = "quantity", nullable = false)
    val quantity: Int,
    
    @Column(name = "unit_price", nullable = false)
    @JsonProperty("unit_price")
    val unitPrice: Double,
    
    @Column(name = "total_amount", nullable = false)
    @JsonProperty("total_amount")
    val totalAmount: Double,
    
    @Column(name = "order_date", nullable = false)
    @JsonProperty("order_date")
    val orderDate: LocalDateTime,
    
    @Column(name = "status", nullable = false)
    val status: String = "PENDING",
    
    @Column(name = "created_at", nullable = false)
    @JsonProperty("created_at")
    val createdAt: LocalDateTime = LocalDateTime.now(),
    
    @Column(name = "updated_at", nullable = false)
    @JsonProperty("updated_at")
    val updatedAt: LocalDateTime = LocalDateTime.now()
)
