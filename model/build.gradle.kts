plugins {
    alias(libs.plugins.kotlin.jvm)
    alias(libs.plugins.kotlin.jpa)
    id("jaco<PERSON>")
}

group = "$group.model"

dependencies {
    // Kotlin
    implementation("org.jetbrains.kotlin:kotlin-reflect:1.9.25")
    implementation("org.jetbrains.kotlin:kotlin-stdlib:1.9.25")

    // Jackson for JSON processing
    implementation("com.fasterxml.jackson.module:jackson-module-kotlin:2.15.4")

    // JPA
    implementation("org.springframework.boot:spring-boot-starter-data-jpa:3.2.5")

    // Test dependencies
    testImplementation("org.springframework.boot:spring-boot-starter-test:3.2.5")
    testImplementation("io.mockk:mockk:1.13.8")
}

tasks {
    withType<org.jetbrains.kotlin.gradle.tasks.KotlinCompile> {
        kotlinOptions {
            jvmTarget = "21"
        }
    }
    
    test {
        useJUnitPlatform()
    }
}
