# suppress inspection "UnusedProperty" for whole file
name=scm-procurement-data-integration
group=com.hellofresh.scm

kotlin.code.style=official

org.gradle.parallel=true

jib.from.image=gcr.io/distroless/java21@sha256:4acc89642c353b429f71b3d0e18dba2b5ea347053a793a8bea7e56065320e08b
jib.from.credHelper=gcr
jib.container.jvmFlags=\
  -XX:+UseContainerSupport \
  -XX:InitialRAMPercentage=70.0 \
  -XX:MaxRAMPercentage=70.0 \
  -Dcom.sun.management.jmxremote=false \
  -Xshare:off \
  -Dlogging.layout=HelloFresh \
  -Dlogging.slack.level=Error
