[versions]
kotlin = "2.2.0"
spring-boot = "3.4.5"
spring-cloud = "2024.0.0"
spring-dependency-management = "1.1.7"
detekt = "1.23.8"
jackson = "2.19.2"
testcontainers = "1.18.3"
sonarqube = "5.1.0.4882"

[libraries]
# Spring Boot
spring-boot-starter-web = { module = "org.springframework.boot:spring-boot-starter-web" }
spring-boot-starter-data-jpa = { module = "org.springframework.boot:spring-boot-starter-data-jpa" }
spring-boot-starter-actuator = { module = "org.springframework.boot:spring-boot-starter-actuator" }
spring-boot-starter-validation = { module = "org.springframework.boot:spring-boot-starter-validation" }
spring-boot-starter-test = { module = "org.springframework.boot:spring-boot-starter-test" }

# Kafka
spring-kafka = { module = "org.springframework.kafka:spring-kafka" }
spring-kafka-test = { module = "org.springframework.kafka:spring-kafka-test" }

# Database
postgresql = { module = "org.postgresql:postgresql", version = "42.7.7" }
h2 = { module = "com.h2database:h2" }

# Jackson
jackson-module-kotlin = { module = "com.fasterxml.jackson.module:jackson-module-kotlin", version.ref = "jackson" }
jackson-databind = { module = "com.fasterxml.jackson.core:jackson-databind", version.ref = "jackson" }

# Kotlin
kotlin-reflect = { module = "org.jetbrains.kotlin:kotlin-reflect" }
kotlin-stdlib = { module = "org.jetbrains.kotlin:kotlin-stdlib" }

# Testing
testcontainers-junit-jupiter = { module = "org.testcontainers:junit-jupiter" }
testcontainers-postgresql = { module = "org.testcontainers:postgresql" }
testcontainers-kafka = { module = "org.testcontainers:kafka" }
mockk = { module = "io.mockk:mockk", version = "1.13.8" }

[plugins]
kotlin-jvm = { id = "org.jetbrains.kotlin.jvm", version.ref = "kotlin" }
kotlin-spring = { id = "org.jetbrains.kotlin.plugin.spring", version.ref = "kotlin" }
kotlin-jpa = { id = "org.jetbrains.kotlin.plugin.jpa", version.ref = "kotlin" }
spring-boot = { id = "org.springframework.boot", version.ref = "spring-boot" }
spring-dependency-management = { id = "io.spring.dependency-management", version.ref = "spring-dependency-management" }
detekt-gradle = { id = "io.gitlab.arturbosch.detekt", version.ref = "detekt" }
sonarqube = { id = "org.sonarqube", version.ref = "sonarqube" }
