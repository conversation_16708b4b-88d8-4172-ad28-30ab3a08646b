[versions]
kotlin = "1.9.25"
spring-boot = "3.2.5"
spring-dependency-management = "1.1.4"
detekt = "1.23.8"
jacoco = "0.8.11"
sonarqube = "4.4.1.3373"

[libraries]
# Spring Boot
spring-boot-starter-web = { module = "org.springframework.boot:spring-boot-starter-web" }
spring-boot-starter-data-jpa = { module = "org.springframework.boot:spring-boot-starter-data-jpa" }
spring-boot-starter-actuator = { module = "org.springframework.boot:spring-boot-starter-actuator" }
spring-boot-starter-validation = { module = "org.springframework.boot:spring-boot-starter-validation" }
spring-boot-starter-test = { module = "org.springframework.boot:spring-boot-starter-test" }

# Kafka
spring-kafka = { module = "org.springframework.kafka:spring-kafka" }
spring-kafka-test = { module = "org.springframework.kafka:spring-kafka-test" }

# Database
postgresql = { module = "org.postgresql:postgresql" }
h2 = { module = "com.h2database:h2" }

# <PERSON>
jackson-module-kotlin = { module = "com.fasterxml.jackson.module:jackson-module-kotlin" }

# Kotlin
kotlin-reflect = { module = "org.jetbrains.kotlin:kotlin-reflect" }
kotlin-stdlib = { module = "org.jetbrains.kotlin:kotlin-stdlib" }

# Testing
testcontainers-junit-jupiter = { module = "org.testcontainers:junit-jupiter" }
testcontainers-postgresql = { module = "org.testcontainers:postgresql" }
testcontainers-kafka = { module = "org.testcontainers:kafka" }
mockk = { module = "io.mockk:mockk" }

[plugins]
kotlin-jvm = { id = "org.jetbrains.kotlin.jvm", version.ref = "kotlin" }
kotlin-spring = { id = "org.jetbrains.kotlin.plugin.spring", version.ref = "kotlin" }
kotlin-jpa = { id = "org.jetbrains.kotlin.plugin.jpa", version.ref = "kotlin" }
spring-boot = { id = "org.springframework.boot", version.ref = "spring-boot" }
spring-dependency-management = { id = "io.spring.dependency-management", version.ref = "spring-dependency-management" }
detekt-gradle = { id = "io.gitlab.arturbosch.detekt", version.ref = "detekt" }
jacocolog = { id = "org.barfuin.gradle.jacocolog", version.ref = "jacoco" }
sonarqube = { id = "org.sonarqube", version.ref = "sonarqube" }
