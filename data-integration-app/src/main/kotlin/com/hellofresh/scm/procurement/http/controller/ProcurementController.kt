package com.hellofresh.scm.procurement.http.controller

import org.springframework.http.ResponseEntity
import org.springframework.web.bind.annotation.*

@RestController
@RequestMapping("/api/v1/procurement")
class ProcurementController {

    @GetMapping("/health")
    fun health(): ResponseEntity<Map<String, String>> {
        return ResponseEntity.ok(mapOf("status" to "UP", "service" to "scm-procurement-http"))
    }

    @GetMapping("/data")
    fun getProcurementData(): ResponseEntity<Map<String, Any>> {
        return ResponseEntity.ok(
            mapOf(
                "message" to "Procurement data endpoint",
                "timestamp" to System.currentTimeMillis()
            )
        )
    }
}
