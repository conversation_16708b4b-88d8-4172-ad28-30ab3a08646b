application:
  brokerAddress: https://kafka-live-hellofresh-live.aivencloud.com:23419
  trustStorePath: classpath:kafka/live-truststore.jks

spring:
  cloud:
    stream:
      kafka:
        binder:
          configuration:
            ssl.truststore.location: ${application.trustStorePath}
            ssl.truststore.password: ${HF_KAFKA_TRUSTSTORE_PASSWORD}
            sasl.mechanism: PLAIN
            security.protocol: SASL_SSL
            ssl.endpoint.identification.algorithm: https
            sasl.jaas.config: "org.apache.kafka.common.security.plain.PlainLoginModule required username=\"${HF_AIVEN_USERNAME}\" password=\"${HF_AIVEN_PASSWORD}\";"
