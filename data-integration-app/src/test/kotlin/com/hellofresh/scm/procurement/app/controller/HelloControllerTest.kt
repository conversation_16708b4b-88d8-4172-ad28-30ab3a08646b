package com.hellofresh.scm.procurement.app.controller

import org.junit.jupiter.api.Test
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.boot.test.autoconfigure.web.servlet.WebMvcTest
import org.springframework.test.context.ActiveProfiles
import org.springframework.test.web.servlet.MockMvc
import org.springframework.test.web.servlet.request.MockMvcRequestBuilders.get
import org.springframework.test.web.servlet.result.MockMvcResultMatchers.*

@WebMvcTest(HelloController::class)
@ActiveProfiles("test")
class HelloControllerTest {

    @Autowired
    private lateinit var mockMvc: MockMvc

    @Test
    fun `should return hello message`() {
        mockMvc.perform(get("/api/v1/hello"))
            .andExpect(status().isOk)
            .andExpect(jsonPath("$.message").value("Hello World from SCM Procurement Data Integration!"))
            .andExpect(jsonPath("$.service").value("data-integration-app"))
            .andExpect(jsonPath("$.timestamp").exists())
    }

    @Test
    fun `should return health status`() {
        mockMvc.perform(get("/api/v1/health"))
            .andExpect(status().isOk)
            .andExpect(jsonPath("$.status").value("UP"))
            .andExpect(jsonPath("$.service").value("data-integration-app"))
    }
}
