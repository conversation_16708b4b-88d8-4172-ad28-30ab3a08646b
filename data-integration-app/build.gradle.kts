plugins {
    alias(libs.plugins.spring.boot)
    alias(libs.plugins.spring.dependency.management)
    alias(libs.plugins.kotlin.jvm)
    alias(libs.plugins.kotlin.spring)
    alias(libs.plugins.kotlin.jpa)
    id("jacoco")
    id("com.google.cloud.tools.jib")
}

group = "$group.data-integration-app"

dependencies {
    // Spring Boot starters
    implementation(libs.spring.boot.starter.web)
    implementation(libs.spring.boot.starter.actuator)
    implementation(libs.spring.boot.starter.validation)
    implementation(libs.spring.boot.starter.data.jpa)
    
    // Kafka
    implementation(libs.spring.kafka)
    
    // Jackson for JSON processing
    implementation(libs.jackson.module.kotlin)
    
    // Kotlin
    implementation(libs.kotlin.reflect)
    implementation(libs.kotlin.stdlib)
    
    // Database
    runtimeOnly(libs.postgresql)
    
    // Model dependency
    implementation(project(":model"))
    
    // Annotation processor
    annotationProcessor("org.springframework.boot:spring-boot-configuration-processor")
    
    // Test dependencies
    testImplementation(libs.spring.boot.starter.test)
    testImplementation(libs.spring.kafka.test)
    testImplementation(libs.testcontainers.junit.jupiter)
    testImplementation(libs.testcontainers.postgresql)
    testImplementation(libs.testcontainers.kafka)
    testImplementation(libs.mockk)
    testImplementation(libs.h2)
}

dependencyManagement {
    imports {
        mavenBom("org.testcontainers:testcontainers-bom:1.19.8")
    }
}

tasks {
    withType<org.jetbrains.kotlin.gradle.tasks.KotlinCompile> {
        compilerOptions {
            jvmTarget.set(org.jetbrains.kotlin.gradle.dsl.JvmTarget.JVM_21)
        }
    }
    
    test {
        useJUnitPlatform {
            excludeTags("integration")
        }
        systemProperty("spring.profiles.active", "test")
    }
}

task<Test>("integrationTest") {
    description = "Runs integration tests."
    group = "verification"
    shouldRunAfter(tasks.test)
    useJUnitPlatform {
        includeTags("integration")
    }
    systemProperty("spring.profiles.active", "test")
}

jib {
    from {
        image = project.property("jib.from.image").toString()
    }
    to {
        image = "data-integration-app:latest"
    }
    container {
        project.findProperty("jib.container.jvmFlags")?.toString()?.split(' ')?.let {
            jvmFlags = it
        }
    }
}
